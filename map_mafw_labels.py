#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to map MAFW emotion labels in the dataset.
Maps: anger->angry, anxiety->anxious, disappointment->disappointed,
      happiness->happy, helplessness->helpless, sadness->sad
"""

import json
import sys
from pathlib import Path

def map_emotion_labels(data):
    """
    Map emotion labels in MAFW samples according to the specified mapping.
    
    Args:
        data: List of samples from the JSON dataset
        
    Returns:
        Modified data with mapped labels
    """
    # Define the label mapping
    label_mapping = {
        'anger': 'angry',
        'anxiety': 'anxious', 
        'disappointment': 'disappointed',
        'happiness': 'happy',
        'helplessness': 'helpless',
        'sadness': 'sad'
    }
    
    modified_count = 0
    
    for sample in data:
        # Check if this is a MAFW sample by looking at the video path
        if 'video' in sample and 'MAFW' in sample['video']:
            # Process conversations to find and map emotion labels
            if 'conversations' in sample:
                for conversation in sample['conversations']:
                    if conversation.get('from') == 'gpt':
                        original_value = conversation['value']
                        # Check if the value matches any of our source labels
                        if original_value in label_mapping:
                            conversation['value'] = label_mapping[original_value]
                            modified_count += 1
                            print(f"Mapped '{original_value}' -> '{label_mapping[original_value]}' in {sample['video']}")
    
    print(f"Total labels modified: {modified_count}")
    return data

def main():
    input_file = "/data/wuyang/merged_dataset_with_difficulty.json"
    output_file = "/data/wuyang/merged_dataset_with_difficulty_mapped.json"
    
    # Check if input file exists
    if not Path(input_file).exists():
        print(f"Error: Input file {input_file} not found")
        sys.exit(1)
    
    print(f"Loading data from {input_file}...")
    
    # Load the JSON data
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"Error loading JSON file: {e}")
        sys.exit(1)
    
    print(f"Loaded {len(data)} samples")
    
    # Map the emotion labels
    print("Mapping emotion labels in MAFW samples...")
    mapped_data = map_emotion_labels(data)
    
    # Save the modified data
    print(f"Saving mapped data to {output_file}...")
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(mapped_data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"Error saving JSON file: {e}")
        sys.exit(1)
    
    print("Label mapping completed successfully!")

if __name__ == "__main__":
    main()
