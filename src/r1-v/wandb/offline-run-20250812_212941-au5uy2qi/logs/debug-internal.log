{"time":"2025-08-12T21:29:41.769886347+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T21:29:41.900370671+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T21:29:41.900600844+08:00","level":"INFO","msg":"stream: created new stream","id":"au5uy2qi"}
{"time":"2025-08-12T21:29:41.900632046+08:00","level":"INFO","msg":"stream: started","id":"au5uy2qi"}
{"time":"2025-08-12T21:29:41.900648298+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"au5uy2qi"}
{"time":"2025-08-12T21:29:41.900746448+08:00","level":"INFO","msg":"sender: started","stream_id":"au5uy2qi"}
{"time":"2025-08-12T21:29:41.900792159+08:00","level":"INFO","msg":"handler: started","stream_id":"au5uy2qi"}
{"time":"2025-08-12T21:29:41.901423452+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
