{"time":"2025-08-12T17:09:55.864763069+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T17:09:55.997903298+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T17:09:55.99814219+08:00","level":"INFO","msg":"stream: created new stream","id":"re9hdjg3"}
{"time":"2025-08-12T17:09:55.998176855+08:00","level":"INFO","msg":"stream: started","id":"re9hdjg3"}
{"time":"2025-08-12T17:09:55.998276366+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"re9hdjg3"}
{"time":"2025-08-12T17:09:55.99834589+08:00","level":"INFO","msg":"sender: started","stream_id":"re9hdjg3"}
{"time":"2025-08-12T17:09:55.998410556+08:00","level":"INFO","msg":"handler: started","stream_id":"re9hdjg3"}
{"time":"2025-08-12T17:09:55.998943022+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-12T17:10:57.119103134+08:00","level":"INFO","msg":"stream: closing","id":"re9hdjg3"}
{"time":"2025-08-12T17:10:57.119496559+08:00","level":"INFO","msg":"handler: closed","stream_id":"re9hdjg3"}
{"time":"2025-08-12T17:10:57.119540889+08:00","level":"INFO","msg":"sender: closed","stream_id":"re9hdjg3"}
{"time":"2025-08-12T17:10:57.119537485+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"re9hdjg3"}
{"time":"2025-08-12T17:10:57.11968623+08:00","level":"INFO","msg":"stream: closed","id":"re9hdjg3"}
