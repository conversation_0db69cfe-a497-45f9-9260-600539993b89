{"time":"2025-08-05T23:56:42.984889121+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-05T23:56:43.115078894+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-05T23:56:43.115323923+08:00","level":"INFO","msg":"stream: created new stream","id":"wowcto9t"}
{"time":"2025-08-05T23:56:43.115354301+08:00","level":"INFO","msg":"stream: started","id":"wowcto9t"}
{"time":"2025-08-05T23:56:43.115444245+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"wowcto9t"}
{"time":"2025-08-05T23:56:43.115522903+08:00","level":"INFO","msg":"handler: started","stream_id":"wowcto9t"}
{"time":"2025-08-05T23:56:43.115579726+08:00","level":"INFO","msg":"sender: started","stream_id":"wowcto9t"}
{"time":"2025-08-05T23:56:43.116445688+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
