{"time":"2025-08-12T18:13:03.39852769+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T18:13:03.533288943+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T18:13:03.533649169+08:00","level":"INFO","msg":"stream: created new stream","id":"7w9qj37m"}
{"time":"2025-08-12T18:13:03.533695496+08:00","level":"INFO","msg":"stream: started","id":"7w9qj37m"}
{"time":"2025-08-12T18:13:03.533789739+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"7w9qj37m"}
{"time":"2025-08-12T18:13:03.533861276+08:00","level":"INFO","msg":"sender: started","stream_id":"7w9qj37m"}
{"time":"2025-08-12T18:13:03.533877655+08:00","level":"INFO","msg":"handler: started","stream_id":"7w9qj37m"}
{"time":"2025-08-12T18:13:03.534881049+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-12T18:13:30.58997067+08:00","level":"INFO","msg":"stream: closing","id":"7w9qj37m"}
{"time":"2025-08-12T18:13:30.590388746+08:00","level":"INFO","msg":"handler: closed","stream_id":"7w9qj37m"}
{"time":"2025-08-12T18:13:30.590441382+08:00","level":"INFO","msg":"sender: closed","stream_id":"7w9qj37m"}
{"time":"2025-08-12T18:13:30.590444612+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"7w9qj37m"}
{"time":"2025-08-12T18:13:30.590587896+08:00","level":"INFO","msg":"stream: closed","id":"7w9qj37m"}
