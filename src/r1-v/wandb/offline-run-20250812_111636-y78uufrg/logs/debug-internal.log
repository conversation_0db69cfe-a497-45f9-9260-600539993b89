{"time":"2025-08-12T11:16:36.933591457+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T11:16:37.063673279+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T11:16:37.064028161+08:00","level":"INFO","msg":"stream: created new stream","id":"y78uufrg"}
{"time":"2025-08-12T11:16:37.064074055+08:00","level":"INFO","msg":"stream: started","id":"y78uufrg"}
{"time":"2025-08-12T11:16:37.064166938+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"y78uufrg"}
{"time":"2025-08-12T11:16:37.06420183+08:00","level":"INFO","msg":"sender: started","stream_id":"y78uufrg"}
{"time":"2025-08-12T11:16:37.064293858+08:00","level":"INFO","msg":"handler: started","stream_id":"y78uufrg"}
{"time":"2025-08-12T11:16:37.065448389+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-12T15:33:41.13488441+08:00","level":"INFO","msg":"stream: closing","id":"y78uufrg"}
{"time":"2025-08-12T15:33:41.135230474+08:00","level":"INFO","msg":"handler: closed","stream_id":"y78uufrg"}
{"time":"2025-08-12T15:33:41.135264946+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"y78uufrg"}
{"time":"2025-08-12T15:33:41.135315312+08:00","level":"INFO","msg":"sender: closed","stream_id":"y78uufrg"}
{"time":"2025-08-12T15:33:41.135412577+08:00","level":"INFO","msg":"stream: closed","id":"y78uufrg"}
