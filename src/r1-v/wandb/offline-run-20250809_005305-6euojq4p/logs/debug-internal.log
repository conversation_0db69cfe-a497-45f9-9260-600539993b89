{"time":"2025-08-09T00:53:05.976357148+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-09T00:53:06.112813072+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-09T00:53:06.113186731+08:00","level":"INFO","msg":"stream: created new stream","id":"6euojq4p"}
{"time":"2025-08-09T00:53:06.113233559+08:00","level":"INFO","msg":"stream: started","id":"6euojq4p"}
{"time":"2025-08-09T00:53:06.113330512+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"6euojq4p"}
{"time":"2025-08-09T00:53:06.113398147+08:00","level":"INFO","msg":"sender: started","stream_id":"6euojq4p"}
{"time":"2025-08-09T00:53:06.113447793+08:00","level":"INFO","msg":"handler: started","stream_id":"6euojq4p"}
{"time":"2025-08-09T00:53:06.114724701+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
