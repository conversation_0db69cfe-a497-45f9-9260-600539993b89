{"time":"2025-08-09T15:47:55.968157414+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-09T15:47:56.102625007+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-09T15:47:56.102953446+08:00","level":"INFO","msg":"stream: created new stream","id":"xas544p8"}
{"time":"2025-08-09T15:47:56.103016415+08:00","level":"INFO","msg":"stream: started","id":"xas544p8"}
{"time":"2025-08-09T15:47:56.103100644+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"xas544p8"}
{"time":"2025-08-09T15:47:56.103198511+08:00","level":"INFO","msg":"sender: started","stream_id":"xas544p8"}
{"time":"2025-08-09T15:47:56.103266489+08:00","level":"INFO","msg":"handler: started","stream_id":"xas544p8"}
{"time":"2025-08-09T15:47:56.104450433+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-09T15:50:31.513577555+08:00","level":"INFO","msg":"stream: closing","id":"xas544p8"}
{"time":"2025-08-09T15:50:31.51395338+08:00","level":"INFO","msg":"handler: closed","stream_id":"xas544p8"}
{"time":"2025-08-09T15:50:31.513982555+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"xas544p8"}
{"time":"2025-08-09T15:50:31.514031083+08:00","level":"INFO","msg":"sender: closed","stream_id":"xas544p8"}
{"time":"2025-08-09T15:50:31.514110141+08:00","level":"INFO","msg":"stream: closed","id":"xas544p8"}
