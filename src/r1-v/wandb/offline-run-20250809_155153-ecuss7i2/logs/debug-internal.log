{"time":"2025-08-09T15:51:53.427355486+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-09T15:51:53.561884684+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-09T15:51:53.562246254+08:00","level":"INFO","msg":"stream: created new stream","id":"ecuss7i2"}
{"time":"2025-08-09T15:51:53.562291515+08:00","level":"INFO","msg":"stream: started","id":"ecuss7i2"}
{"time":"2025-08-09T15:51:53.562371617+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"ecuss7i2"}
{"time":"2025-08-09T15:51:53.562443807+08:00","level":"INFO","msg":"handler: started","stream_id":"ecuss7i2"}
{"time":"2025-08-09T15:51:53.562486119+08:00","level":"INFO","msg":"sender: started","stream_id":"ecuss7i2"}
{"time":"2025-08-09T15:51:53.563421481+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-09T16:23:59.561531103+08:00","level":"INFO","msg":"stream: closing","id":"ecuss7i2"}
{"time":"2025-08-09T16:23:59.561853949+08:00","level":"INFO","msg":"handler: closed","stream_id":"ecuss7i2"}
{"time":"2025-08-09T16:23:59.561915233+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"ecuss7i2"}
{"time":"2025-08-09T16:23:59.561923921+08:00","level":"INFO","msg":"sender: closed","stream_id":"ecuss7i2"}
{"time":"2025-08-09T16:23:59.562028448+08:00","level":"INFO","msg":"stream: closed","id":"ecuss7i2"}
