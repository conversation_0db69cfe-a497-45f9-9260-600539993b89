{"time":"2025-08-12T18:24:07.984132188+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T18:24:08.120505878+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T18:24:08.120804607+08:00","level":"INFO","msg":"stream: created new stream","id":"c02d4j4w"}
{"time":"2025-08-12T18:24:08.120847228+08:00","level":"INFO","msg":"stream: started","id":"c02d4j4w"}
{"time":"2025-08-12T18:24:08.120889479+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"c02d4j4w"}
{"time":"2025-08-12T18:24:08.120955843+08:00","level":"INFO","msg":"sender: started","stream_id":"c02d4j4w"}
{"time":"2025-08-12T18:24:08.121024713+08:00","level":"INFO","msg":"handler: started","stream_id":"c02d4j4w"}
{"time":"2025-08-12T18:24:08.122095269+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
