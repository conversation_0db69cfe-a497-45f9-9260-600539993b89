{"time":"2025-08-11T19:46:21.116008033+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-11T19:46:21.24686811+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-11T19:46:21.247188795+08:00","level":"INFO","msg":"stream: created new stream","id":"0idwgy5y"}
{"time":"2025-08-11T19:46:21.247229253+08:00","level":"INFO","msg":"stream: started","id":"0idwgy5y"}
{"time":"2025-08-11T19:46:21.247322218+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"0idwgy5y"}
{"time":"2025-08-11T19:46:21.247394934+08:00","level":"INFO","msg":"handler: started","stream_id":"0idwgy5y"}
{"time":"2025-08-11T19:46:21.247434963+08:00","level":"INFO","msg":"sender: started","stream_id":"0idwgy5y"}
{"time":"2025-08-11T19:46:21.248494365+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
