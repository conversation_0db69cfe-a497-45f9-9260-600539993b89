{"time":"2025-08-09T17:01:14.448014833+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-09T17:01:14.572569631+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-09T17:01:14.572889048+08:00","level":"INFO","msg":"stream: created new stream","id":"a3vm5dte"}
{"time":"2025-08-09T17:01:14.57292807+08:00","level":"INFO","msg":"stream: started","id":"a3vm5dte"}
{"time":"2025-08-09T17:01:14.572970514+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"a3vm5dte"}
{"time":"2025-08-09T17:01:14.573118548+08:00","level":"INFO","msg":"sender: started","stream_id":"a3vm5dte"}
{"time":"2025-08-09T17:01:14.573073988+08:00","level":"INFO","msg":"handler: started","stream_id":"a3vm5dte"}
{"time":"2025-08-09T17:01:14.574100392+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-09T18:40:08.629738117+08:00","level":"INFO","msg":"stream: closing","id":"a3vm5dte"}
{"time":"2025-08-09T18:40:08.629928452+08:00","level":"INFO","msg":"handler: closed","stream_id":"a3vm5dte"}
{"time":"2025-08-09T18:40:08.629944079+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"a3vm5dte"}
{"time":"2025-08-09T18:40:08.629958745+08:00","level":"INFO","msg":"sender: closed","stream_id":"a3vm5dte"}
{"time":"2025-08-09T18:40:08.630086304+08:00","level":"INFO","msg":"stream: closed","id":"a3vm5dte"}
