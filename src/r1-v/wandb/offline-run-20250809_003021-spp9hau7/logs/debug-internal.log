{"time":"2025-08-09T00:30:22.00760876+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-09T00:30:22.135848179+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-09T00:30:22.136148386+08:00","level":"INFO","msg":"stream: created new stream","id":"spp9hau7"}
{"time":"2025-08-09T00:30:22.136182297+08:00","level":"INFO","msg":"stream: started","id":"spp9hau7"}
{"time":"2025-08-09T00:30:22.136278517+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"spp9hau7"}
{"time":"2025-08-09T00:30:22.136351268+08:00","level":"INFO","msg":"handler: started","stream_id":"spp9hau7"}
{"time":"2025-08-09T00:30:22.136411146+08:00","level":"INFO","msg":"sender: started","stream_id":"spp9hau7"}
{"time":"2025-08-09T00:30:22.137197548+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
