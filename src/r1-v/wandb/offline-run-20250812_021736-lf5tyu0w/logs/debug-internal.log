{"time":"2025-08-12T02:17:37.168204242+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T02:17:37.302158827+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T02:17:37.302482618+08:00","level":"INFO","msg":"stream: created new stream","id":"lf5tyu0w"}
{"time":"2025-08-12T02:17:37.302524799+08:00","level":"INFO","msg":"stream: started","id":"lf5tyu0w"}
{"time":"2025-08-12T02:17:37.302635029+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"lf5tyu0w"}
{"time":"2025-08-12T02:17:37.302699268+08:00","level":"INFO","msg":"handler: started","stream_id":"lf5tyu0w"}
{"time":"2025-08-12T02:17:37.302690735+08:00","level":"INFO","msg":"sender: started","stream_id":"lf5tyu0w"}
{"time":"2025-08-12T02:17:37.303821132+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-12T02:19:02.421324024+08:00","level":"INFO","msg":"stream: closing","id":"lf5tyu0w"}
{"time":"2025-08-12T02:19:02.42164652+08:00","level":"INFO","msg":"handler: closed","stream_id":"lf5tyu0w"}
{"time":"2025-08-12T02:19:02.421686912+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"lf5tyu0w"}
{"time":"2025-08-12T02:19:02.421746398+08:00","level":"INFO","msg":"sender: closed","stream_id":"lf5tyu0w"}
{"time":"2025-08-12T02:19:02.421860785+08:00","level":"INFO","msg":"stream: closed","id":"lf5tyu0w"}
