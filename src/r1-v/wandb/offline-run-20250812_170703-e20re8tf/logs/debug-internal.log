{"time":"2025-08-12T17:07:03.912770331+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T17:07:04.045050357+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T17:07:04.045330152+08:00","level":"INFO","msg":"stream: created new stream","id":"e20re8tf"}
{"time":"2025-08-12T17:07:04.04536915+08:00","level":"INFO","msg":"stream: started","id":"e20re8tf"}
{"time":"2025-08-12T17:07:04.045468755+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"e20re8tf"}
{"time":"2025-08-12T17:07:04.045529851+08:00","level":"INFO","msg":"sender: started","stream_id":"e20re8tf"}
{"time":"2025-08-12T17:07:04.045608807+08:00","level":"INFO","msg":"handler: started","stream_id":"e20re8tf"}
{"time":"2025-08-12T17:07:04.046640836+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-12T17:07:31.092858058+08:00","level":"INFO","msg":"stream: closing","id":"e20re8tf"}
{"time":"2025-08-12T17:07:31.093062419+08:00","level":"INFO","msg":"handler: closed","stream_id":"e20re8tf"}
{"time":"2025-08-12T17:07:31.093081089+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"e20re8tf"}
{"time":"2025-08-12T17:07:31.093093614+08:00","level":"INFO","msg":"sender: closed","stream_id":"e20re8tf"}
{"time":"2025-08-12T17:07:31.093210212+08:00","level":"INFO","msg":"stream: closed","id":"e20re8tf"}
