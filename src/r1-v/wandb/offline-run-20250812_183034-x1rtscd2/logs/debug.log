2025-08-12 18:30:34,226 INFO    MainThread:224234 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-08-12 18:30:34,227 INFO    MainThread:224234 [wandb_setup.py:_flush():80] Configure stats pid to 224234
2025-08-12 18:30:34,227 INFO    MainThread:224234 [wandb_setup.py:_flush():80] Loading settings from /home/<USER>/.config/wandb/settings
2025-08-12 18:30:34,227 INFO    MainThread:224234 [wandb_setup.py:_flush():80] Loading settings from /data/wuyang/R1-Omni-main/src/r1-v/wandb/settings
2025-08-12 18:30:34,227 INFO    MainThread:224234 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-08-12 18:30:34,227 INFO    MainThread:224234 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /data/wuyang/R1-Omni-main/src/r1-v/wandb/offline-run-20250812_183034-x1rtscd2/logs/debug.log
2025-08-12 18:30:34,227 INFO    MainThread:224234 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /data/wuyang/R1-Omni-main/src/r1-v/wandb/offline-run-20250812_183034-x1rtscd2/logs/debug-internal.log
2025-08-12 18:30:34,227 INFO    MainThread:224234 [wandb_init.py:init():830] calling init triggers
2025-08-12 18:30:34,227 INFO    MainThread:224234 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-08-12 18:30:34,227 INFO    MainThread:224234 [wandb_init.py:init():871] starting backend
2025-08-12 18:30:34,436 INFO    MainThread:224234 [wandb_init.py:init():874] sending inform_init request
2025-08-12 18:30:34,439 INFO    MainThread:224234 [wandb_init.py:init():882] backend started and connected
2025-08-12 18:30:34,441 INFO    MainThread:224234 [wandb_init.py:init():953] updated telemetry
2025-08-12 18:30:34,442 INFO    MainThread:224234 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
2025-08-12 18:30:34,659 INFO    MainThread:224234 [wandb_init.py:init():1029] starting run threads in backend
2025-08-12 18:30:34,907 INFO    MainThread:224234 [wandb_run.py:_console_start():2458] atexit reg
2025-08-12 18:30:34,907 INFO    MainThread:224234 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-08-12 18:30:34,907 INFO    MainThread:224234 [wandb_run.py:_redirect():2375] Wrapping output streams.
2025-08-12 18:30:34,907 INFO    MainThread:224234 [wandb_run.py:_redirect():2398] Redirects installed.
2025-08-12 18:30:34,909 INFO    MainThread:224234 [wandb_init.py:init():1075] run started, returning control to user process
2025-08-12 18:30:34,912 INFO    MainThread:224234 [wandb_run.py:_config_callback():1363] config_cb None None {'vocab_size': 151936, 'max_position_embeddings': 32768, 'hidden_size': 896, 'intermediate_size': 4864, 'num_hidden_layers': 24, 'num_attention_heads': 14, 'use_sliding_window': False, 'sliding_window': None, 'max_window_layers': 21, 'num_key_value_heads': 2, 'hidden_act': 'silu', 'initializer_range': 0.02, 'rms_norm_eps': 1e-06, 'use_cache': True, 'rope_theta': 1000000.0, 'rope_scaling': None, 'attention_dropout': 0.0, 'layer_types': ['full_attention', 'full_attention', 'full_attention', 'full_attention', 'full_attention', 'full_attention', 'full_attention', 'full_attention', 'full_attention', 'full_attention', 'full_attention', 'full_attention', 'full_attention', 'full_attention', 'full_attention', 'full_attention', 'full_attention', 'full_attention', 'full_attention', 'full_attention', 'full_attention', 'full_attention', 'full_attention', 'full_attention'], 'return_dict': True, 'output_hidden_states': False, 'torchscript': False, 'torch_dtype': 'bfloat16', 'use_bfloat16': False, 'tf_legacy_loss': False, 'pruned_heads': {}, 'tie_word_embeddings': True, 'chunk_size_feed_forward': 0, 'is_encoder_decoder': False, 'is_decoder': False, 'cross_attention_hidden_size': None, 'add_cross_attention': False, 'tie_encoder_decoder': False, 'max_length': 20, 'min_length': 0, 'do_sample': False, 'early_stopping': False, 'num_beams': 1, 'num_beam_groups': 1, 'diversity_penalty': 0.0, 'temperature': 0.9, 'top_k': 50, 'top_p': 1.0, 'typical_p': 1.0, 'repetition_penalty': 1.0, 'length_penalty': 1.0, 'no_repeat_ngram_size': 0, 'encoder_no_repeat_ngram_size': 0, 'bad_words_ids': None, 'num_return_sequences': 1, 'output_scores': False, 'return_dict_in_generate': False, 'forced_bos_token_id': None, 'forced_eos_token_id': None, 'remove_invalid_values': False, 'exponential_decay_length_penalty': None, 'suppress_tokens': None, 'begin_suppress_tokens': None, 'architectures': ['HumanOmniQwen2ForCausalLM'], 'finetuning_task': None, 'id2label': {0: 'LABEL_0', 1: 'LABEL_1'}, 'label2id': {'LABEL_0': 0, 'LABEL_1': 1}, 'tokenizer_class': None, 'prefix': None, 'bos_token_id': 151643, 'pad_token_id': None, 'eos_token_id': 151645, 'sep_token_id': None, 'decoder_start_token_id': None, 'task_specific_params': None, 'problem_type': None, '_name_or_path': '/data/wuyang/PLM/EMER-SFT-0.5B', 'transformers_version': '4.53.1', 'audio_hidden_size': 1280, 'freeze_mm_mlp_adapter': False, 'hyper_layers': [15, 22], 'image_aspect_ratio': 'pad', 'mm_audio_projector_type': 'mlp2x_gelu', 'mm_audio_tower': '/data/wuyang/PLM/whisper-large-v3', 'mm_hidden_size': 768, 'mm_projector_lr': None, 'mm_projector_type': 'all_in_one_small', 'mm_tunable_parts': 'mm_mlp_adapter,audio_projector,mm_language_model', 'mm_use_x_start_end': True, 'mm_vision_select_feature': 'patch', 'mm_vision_select_layer': -2, 'mm_vision_tower': '/data/wuyang/PLM/siglip-base-patch16-224', 'model_type': 'HumanOmni_qwen2', 'num_frames': 8, 'tokenizer_model_max_length': 2048, 'tokenizer_padding_side': 'right', 'tune_mm_mlp_adapter': False, 'use_mm_proj': True, 'output_attentions': False, 'output_dir': '/data/wuyang/R1-Omni-main/Outputs/eval_test_w5w5_test3', 'overwrite_output_dir': False, 'do_train': False, 'do_eval': False, 'do_predict': False, 'eval_strategy': 'no', 'prediction_loss_only': False, 'per_device_train_batch_size': 1, 'per_device_eval_batch_size': 8, 'per_gpu_train_batch_size': None, 'per_gpu_eval_batch_size': None, 'gradient_accumulation_steps': 2, 'eval_accumulation_steps': None, 'eval_delay': 0, 'torch_empty_cache_steps': None, 'learning_rate': 1e-06, 'weight_decay': 0.0, 'adam_beta1': 0.9, 'adam_beta2': 0.999, 'adam_epsilon': 1e-08, 'max_grad_norm': 1.0, 'num_train_epochs': 2.0, 'max_steps': -1, 'lr_scheduler_type': 'linear', 'lr_scheduler_kwargs': {}, 'warmup_ratio': 0.0, 'warmup_steps': 0, 'log_level': 'passive', 'log_level_replica': 'warning', 'log_on_each_node': True, 'logging_dir': '/data/wuyang/R1-Omni-main/Outputs/eval_test_w5w5_test3/runs/Aug12_18-30-14_localhost.localdomain', 'logging_strategy': 'steps', 'logging_first_step': False, 'logging_steps': 1.0, 'logging_nan_inf_filter': True, 'save_strategy': 'steps', 'save_steps': 500, 'save_total_limit': None, 'save_safetensors': True, 'save_on_each_node': False, 'save_only_model': True, 'restore_callback_states_from_checkpoint': False, 'no_cuda': False, 'use_cpu': False, 'use_mps_device': False, 'seed': 42, 'data_seed': None, 'jit_mode_eval': False, 'use_ipex': False, 'bf16': True, 'fp16': False, 'fp16_opt_level': 'O1', 'half_precision_backend': 'auto', 'bf16_full_eval': False, 'fp16_full_eval': False, 'tf32': None, 'local_rank': 0, 'ddp_backend': None, 'tpu_num_cores': None, 'tpu_metrics_debug': False, 'debug': [], 'dataloader_drop_last': False, 'eval_steps': None, 'dataloader_num_workers': 0, 'dataloader_prefetch_factor': None, 'past_index': -1, 'run_name': 'Qwen2-VL-2B-GRPO-emotion', 'disable_tqdm': False, 'remove_unused_columns': False, 'label_names': None, 'load_best_model_at_end': False, 'metric_for_best_model': None, 'greater_is_better': None, 'ignore_data_skip': False, 'fsdp': [], 'fsdp_min_num_params': 0, 'fsdp_config': {'min_num_params': 0, 'xla': False, 'xla_fsdp_v2': False, 'xla_fsdp_grad_ckpt': False}, 'fsdp_transformer_layer_cls_to_wrap': None, 'accelerator_config': {'split_batches': False, 'dispatch_batches': None, 'even_batches': True, 'use_seedable_sampler': True, 'non_blocking': False, 'gradient_accumulation_kwargs': None}, 'deepspeed': 'local_scripts/zero3.json', 'label_smoothing_factor': 0.0, 'optim': 'adamw_torch', 'optim_args': None, 'adafactor': False, 'group_by_length': False, 'length_column_name': 'length', 'report_to': ['wandb'], 'ddp_find_unused_parameters': None, 'ddp_bucket_cap_mb': None, 'ddp_broadcast_buffers': None, 'dataloader_pin_memory': True, 'dataloader_persistent_workers': False, 'skip_memory_metrics': True, 'use_legacy_prediction_loop': False, 'push_to_hub': False, 'resume_from_checkpoint': None, 'hub_model_id': None, 'hub_strategy': 'every_save', 'hub_token': '<HUB_TOKEN>', 'hub_private_repo': None, 'hub_always_push': False, 'hub_revision': None, 'gradient_checkpointing': False, 'gradient_checkpointing_kwargs': None, 'include_inputs_for_metrics': False, 'include_for_metrics': [], 'eval_do_concat_batches': True, 'fp16_backend': 'auto', 'push_to_hub_model_id': None, 'push_to_hub_organization': None, 'push_to_hub_token': '<PUSH_TO_HUB_TOKEN>', 'mp_parameters': '', 'auto_find_batch_size': False, 'full_determinism': False, 'torchdynamo': None, 'ray_scope': 'last', 'ddp_timeout': 1800, 'torch_compile': False, 'torch_compile_backend': None, 'torch_compile_mode': None, 'include_tokens_per_second': False, 'include_num_input_tokens_seen': False, 'neftune_noise_alpha': None, 'optim_target_modules': None, 'batch_eval_metrics': False, 'eval_on_start': False, 'use_liger_kernel': False, 'liger_kernel_config': None, 'eval_use_gather_object': False, 'average_tokens_across_devices': False, 'model_init_kwargs': None, 'max_prompt_length': 512, 'num_generations': 8, 'max_completion_length': 512, 'use_vllm': False, 'vllm_device': 'auto', 'vllm_gpu_memory_utilization': 0.9, 'beta': 0.04}
2025-08-12 18:30:34,921 INFO    MainThread:224234 [wandb_config.py:__setitem__():154] [no run ID] config set model/num_parameters = 0 - <bound method Run._config_callback of <wandb.sdk.wandb_run.Run object at 0x7fcb39902cb0>>
2025-08-12 18:30:34,921 INFO    MainThread:224234 [wandb_run.py:_config_callback():1363] config_cb model/num_parameters 0 None
