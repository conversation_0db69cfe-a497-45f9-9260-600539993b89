{"time":"2025-08-12T18:30:34.450036213+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T18:30:34.585284911+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T18:30:34.655296211+08:00","level":"INFO","msg":"stream: created new stream","id":"x1rtscd2"}
{"time":"2025-08-12T18:30:34.65535057+08:00","level":"INFO","msg":"stream: started","id":"x1rtscd2"}
{"time":"2025-08-12T18:30:34.655396342+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"x1rtscd2"}
{"time":"2025-08-12T18:30:34.655449445+08:00","level":"INFO","msg":"sender: started","stream_id":"x1rtscd2"}
{"time":"2025-08-12T18:30:34.655539124+08:00","level":"INFO","msg":"handler: started","stream_id":"x1rtscd2"}
{"time":"2025-08-12T18:30:34.656797636+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
