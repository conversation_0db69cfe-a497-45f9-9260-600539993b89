{"time":"2025-08-06T00:01:56.20452814+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-06T00:01:56.340085282+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-06T00:01:56.340425376+08:00","level":"INFO","msg":"stream: created new stream","id":"pdro5dro"}
{"time":"2025-08-06T00:01:56.340470154+08:00","level":"INFO","msg":"stream: started","id":"pdro5dro"}
{"time":"2025-08-06T00:01:56.340553221+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"pdro5dro"}
{"time":"2025-08-06T00:01:56.340603051+08:00","level":"INFO","msg":"handler: started","stream_id":"pdro5dro"}
{"time":"2025-08-06T00:01:56.340668213+08:00","level":"INFO","msg":"sender: started","stream_id":"pdro5dro"}
{"time":"2025-08-06T00:01:56.341906002+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-08T01:50:48.843583011+08:00","level":"INFO","msg":"stream: closing","id":"pdro5dro"}
{"time":"2025-08-08T01:50:48.843963301+08:00","level":"INFO","msg":"handler: closed","stream_id":"pdro5dro"}
{"time":"2025-08-08T01:50:48.844078722+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"pdro5dro"}
{"time":"2025-08-08T01:50:48.844112008+08:00","level":"INFO","msg":"sender: closed","stream_id":"pdro5dro"}
{"time":"2025-08-08T01:50:48.844184969+08:00","level":"INFO","msg":"stream: closed","id":"pdro5dro"}
