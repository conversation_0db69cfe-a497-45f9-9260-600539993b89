{"time":"2025-08-12T18:26:38.731102275+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T18:26:38.865823287+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T18:26:38.866199652+08:00","level":"INFO","msg":"stream: created new stream","id":"xpn08h1g"}
{"time":"2025-08-12T18:26:38.866249069+08:00","level":"INFO","msg":"stream: started","id":"xpn08h1g"}
{"time":"2025-08-12T18:26:38.866341122+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"xpn08h1g"}
{"time":"2025-08-12T18:26:38.866394232+08:00","level":"INFO","msg":"sender: started","stream_id":"xpn08h1g"}
{"time":"2025-08-12T18:26:38.86646146+08:00","level":"INFO","msg":"handler: started","stream_id":"xpn08h1g"}
{"time":"2025-08-12T18:26:38.932909364+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-12T18:27:49.179455286+08:00","level":"INFO","msg":"stream: closing","id":"xpn08h1g"}
{"time":"2025-08-12T18:27:49.179757963+08:00","level":"INFO","msg":"handler: closed","stream_id":"xpn08h1g"}
{"time":"2025-08-12T18:27:49.179789999+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"xpn08h1g"}
{"time":"2025-08-12T18:27:49.179799424+08:00","level":"INFO","msg":"sender: closed","stream_id":"xpn08h1g"}
{"time":"2025-08-12T18:27:49.179903711+08:00","level":"INFO","msg":"stream: closed","id":"xpn08h1g"}
