{"time":"2025-08-12T17:54:54.358112387+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T17:54:54.493771756+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T17:54:54.494095945+08:00","level":"INFO","msg":"stream: created new stream","id":"awu2z9s9"}
{"time":"2025-08-12T17:54:54.49413917+08:00","level":"INFO","msg":"stream: started","id":"awu2z9s9"}
{"time":"2025-08-12T17:54:54.49422578+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"awu2z9s9"}
{"time":"2025-08-12T17:54:54.494310289+08:00","level":"INFO","msg":"sender: started","stream_id":"awu2z9s9"}
{"time":"2025-08-12T17:54:54.494369963+08:00","level":"INFO","msg":"handler: started","stream_id":"awu2z9s9"}
{"time":"2025-08-12T17:54:54.495391313+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-12T17:56:25.641246967+08:00","level":"INFO","msg":"stream: closing","id":"awu2z9s9"}
{"time":"2025-08-12T17:56:25.64151171+08:00","level":"INFO","msg":"handler: closed","stream_id":"awu2z9s9"}
{"time":"2025-08-12T17:56:25.641532565+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"awu2z9s9"}
{"time":"2025-08-12T17:56:25.641543558+08:00","level":"INFO","msg":"sender: closed","stream_id":"awu2z9s9"}
{"time":"2025-08-12T17:56:25.641622532+08:00","level":"INFO","msg":"stream: closed","id":"awu2z9s9"}
