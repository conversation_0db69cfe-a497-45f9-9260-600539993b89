{"time":"2025-08-12T01:59:03.18196452+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T01:59:03.316443342+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T01:59:03.316794568+08:00","level":"INFO","msg":"stream: created new stream","id":"dhnd0129"}
{"time":"2025-08-12T01:59:03.316839728+08:00","level":"INFO","msg":"stream: started","id":"dhnd0129"}
{"time":"2025-08-12T01:59:03.316938225+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"dhnd0129"}
{"time":"2025-08-12T01:59:03.317052002+08:00","level":"INFO","msg":"sender: started","stream_id":"dhnd0129"}
{"time":"2025-08-12T01:59:03.317032789+08:00","level":"INFO","msg":"handler: started","stream_id":"dhnd0129"}
{"time":"2025-08-12T01:59:03.31807949+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-12T02:06:01.863424234+08:00","level":"INFO","msg":"stream: closing","id":"dhnd0129"}
{"time":"2025-08-12T02:06:01.863745252+08:00","level":"INFO","msg":"handler: closed","stream_id":"dhnd0129"}
{"time":"2025-08-12T02:06:01.863784223+08:00","level":"INFO","msg":"sender: closed","stream_id":"dhnd0129"}
{"time":"2025-08-12T02:06:01.863784947+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"dhnd0129"}
{"time":"2025-08-12T02:06:01.863938219+08:00","level":"INFO","msg":"stream: closed","id":"dhnd0129"}
