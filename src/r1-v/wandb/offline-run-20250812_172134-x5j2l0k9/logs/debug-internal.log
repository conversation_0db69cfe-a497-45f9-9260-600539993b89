{"time":"2025-08-12T17:21:34.91379004+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T17:21:35.045727612+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T17:21:35.045941264+08:00","level":"INFO","msg":"stream: created new stream","id":"x5j2l0k9"}
{"time":"2025-08-12T17:21:35.045973447+08:00","level":"INFO","msg":"stream: started","id":"x5j2l0k9"}
{"time":"2025-08-12T17:21:35.046020464+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"x5j2l0k9"}
{"time":"2025-08-12T17:21:35.046082247+08:00","level":"INFO","msg":"handler: started","stream_id":"x5j2l0k9"}
{"time":"2025-08-12T17:21:35.046157675+08:00","level":"INFO","msg":"sender: started","stream_id":"x5j2l0k9"}
{"time":"2025-08-12T17:21:35.046984412+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-12T17:22:30.144371961+08:00","level":"INFO","msg":"stream: closing","id":"x5j2l0k9"}
{"time":"2025-08-12T17:22:30.144699865+08:00","level":"INFO","msg":"handler: closed","stream_id":"x5j2l0k9"}
{"time":"2025-08-12T17:22:30.144743057+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"x5j2l0k9"}
{"time":"2025-08-12T17:22:30.144751654+08:00","level":"INFO","msg":"sender: closed","stream_id":"x5j2l0k9"}
{"time":"2025-08-12T17:22:30.144828711+08:00","level":"INFO","msg":"stream: closed","id":"x5j2l0k9"}
