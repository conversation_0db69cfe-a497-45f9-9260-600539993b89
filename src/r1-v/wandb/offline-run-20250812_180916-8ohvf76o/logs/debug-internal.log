{"time":"2025-08-12T18:09:17.195894863+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T18:09:17.325946923+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T18:09:17.326154381+08:00","level":"INFO","msg":"stream: created new stream","id":"8ohvf76o"}
{"time":"2025-08-12T18:09:17.32618408+08:00","level":"INFO","msg":"stream: started","id":"8ohvf76o"}
{"time":"2025-08-12T18:09:17.326273604+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"8ohvf76o"}
{"time":"2025-08-12T18:09:17.326297985+08:00","level":"INFO","msg":"sender: started","stream_id":"8ohvf76o"}
{"time":"2025-08-12T18:09:17.326372426+08:00","level":"INFO","msg":"handler: started","stream_id":"8ohvf76o"}
{"time":"2025-08-12T18:09:17.327257275+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-12T18:09:45.410353521+08:00","level":"INFO","msg":"stream: closing","id":"8ohvf76o"}
{"time":"2025-08-12T18:09:45.410718483+08:00","level":"INFO","msg":"handler: closed","stream_id":"8ohvf76o"}
{"time":"2025-08-12T18:09:45.410744852+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"8ohvf76o"}
{"time":"2025-08-12T18:09:45.410757584+08:00","level":"INFO","msg":"sender: closed","stream_id":"8ohvf76o"}
{"time":"2025-08-12T18:09:45.410874006+08:00","level":"INFO","msg":"stream: closed","id":"8ohvf76o"}
