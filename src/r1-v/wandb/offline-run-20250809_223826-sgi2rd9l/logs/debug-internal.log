{"time":"2025-08-09T22:38:26.644502165+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-09T22:38:26.770352102+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-09T22:38:26.770708378+08:00","level":"INFO","msg":"stream: created new stream","id":"sgi2rd9l"}
{"time":"2025-08-09T22:38:26.770752754+08:00","level":"INFO","msg":"stream: started","id":"sgi2rd9l"}
{"time":"2025-08-09T22:38:26.770865948+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"sgi2rd9l"}
{"time":"2025-08-09T22:38:26.770911804+08:00","level":"INFO","msg":"handler: started","stream_id":"sgi2rd9l"}
{"time":"2025-08-09T22:38:26.770958756+08:00","level":"INFO","msg":"sender: started","stream_id":"sgi2rd9l"}
{"time":"2025-08-09T22:38:26.772050277+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
