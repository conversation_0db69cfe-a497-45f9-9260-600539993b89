{"time":"2025-08-09T00:41:34.001644894+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-09T00:41:34.122588967+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-09T00:41:34.122826101+08:00","level":"INFO","msg":"stream: created new stream","id":"qoqac9fo"}
{"time":"2025-08-09T00:41:34.122855179+08:00","level":"INFO","msg":"stream: started","id":"qoqac9fo"}
{"time":"2025-08-09T00:41:34.122937913+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"qoqac9fo"}
{"time":"2025-08-09T00:41:34.123028521+08:00","level":"INFO","msg":"handler: started","stream_id":"qoqac9fo"}
{"time":"2025-08-09T00:41:34.122985041+08:00","level":"INFO","msg":"sender: started","stream_id":"qoqac9fo"}
{"time":"2025-08-09T00:41:34.123823112+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-09T00:41:48.144381033+08:00","level":"INFO","msg":"stream: closing","id":"qoqac9fo"}
{"time":"2025-08-09T00:41:48.144697991+08:00","level":"INFO","msg":"handler: closed","stream_id":"qoqac9fo"}
{"time":"2025-08-09T00:41:48.14472051+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"qoqac9fo"}
{"time":"2025-08-09T00:41:48.144747796+08:00","level":"INFO","msg":"sender: closed","stream_id":"qoqac9fo"}
{"time":"2025-08-09T00:41:48.144811712+08:00","level":"INFO","msg":"stream: closed","id":"qoqac9fo"}
