{"time":"2025-08-10T11:56:59.762253717+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-10T11:56:59.894474259+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-10T11:56:59.894762413+08:00","level":"INFO","msg":"stream: created new stream","id":"dnxh5zmg"}
{"time":"2025-08-10T11:56:59.894801368+08:00","level":"INFO","msg":"stream: started","id":"dnxh5zmg"}
{"time":"2025-08-10T11:56:59.894891822+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"dnxh5zmg"}
{"time":"2025-08-10T11:56:59.894967542+08:00","level":"INFO","msg":"sender: started","stream_id":"dnxh5zmg"}
{"time":"2025-08-10T11:56:59.895186679+08:00","level":"INFO","msg":"handler: started","stream_id":"dnxh5zmg"}
{"time":"2025-08-10T11:56:59.896135267+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-11T13:06:07.697783706+08:00","level":"INFO","msg":"stream: closing","id":"dnxh5zmg"}
{"time":"2025-08-11T13:06:07.698250957+08:00","level":"INFO","msg":"handler: closed","stream_id":"dnxh5zmg"}
{"time":"2025-08-11T13:06:07.698293925+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"dnxh5zmg"}
{"time":"2025-08-11T13:06:07.698301424+08:00","level":"INFO","msg":"sender: closed","stream_id":"dnxh5zmg"}
{"time":"2025-08-11T13:06:07.698415868+08:00","level":"INFO","msg":"stream: closed","id":"dnxh5zmg"}
