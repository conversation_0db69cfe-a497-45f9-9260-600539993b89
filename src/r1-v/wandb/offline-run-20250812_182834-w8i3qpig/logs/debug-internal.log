{"time":"2025-08-12T18:28:34.674319884+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T18:28:34.815408723+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T18:28:34.815713193+08:00","level":"INFO","msg":"stream: created new stream","id":"w8i3qpig"}
{"time":"2025-08-12T18:28:34.81575856+08:00","level":"INFO","msg":"stream: started","id":"w8i3qpig"}
{"time":"2025-08-12T18:28:34.815796368+08:00","level":"INFO","msg":"handler: started","stream_id":"w8i3qpig"}
{"time":"2025-08-12T18:28:34.81580244+08:00","level":"INFO","msg":"sender: started","stream_id":"w8i3qpig"}
{"time":"2025-08-12T18:28:34.815860763+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"w8i3qpig"}
{"time":"2025-08-12T18:28:34.816861789+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-12T18:29:35.29373924+08:00","level":"INFO","msg":"stream: closing","id":"w8i3qpig"}
{"time":"2025-08-12T18:29:35.293960616+08:00","level":"INFO","msg":"handler: closed","stream_id":"w8i3qpig"}
{"time":"2025-08-12T18:29:35.293990259+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"w8i3qpig"}
{"time":"2025-08-12T18:29:35.29404709+08:00","level":"INFO","msg":"sender: closed","stream_id":"w8i3qpig"}
{"time":"2025-08-12T18:29:35.294115246+08:00","level":"INFO","msg":"stream: closed","id":"w8i3qpig"}
