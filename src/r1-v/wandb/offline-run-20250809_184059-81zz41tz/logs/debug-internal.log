{"time":"2025-08-09T18:41:00.122606257+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-09T18:41:00.256326908+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-09T18:41:00.256640544+08:00","level":"INFO","msg":"stream: created new stream","id":"81zz41tz"}
{"time":"2025-08-09T18:41:00.25668505+08:00","level":"INFO","msg":"stream: started","id":"81zz41tz"}
{"time":"2025-08-09T18:41:00.256773198+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"81zz41tz"}
{"time":"2025-08-09T18:41:00.256791682+08:00","level":"INFO","msg":"sender: started","stream_id":"81zz41tz"}
{"time":"2025-08-09T18:41:00.256841764+08:00","level":"INFO","msg":"handler: started","stream_id":"81zz41tz"}
{"time":"2025-08-09T18:41:00.257804663+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-09T22:30:45.22187202+08:00","level":"INFO","msg":"stream: closing","id":"81zz41tz"}
{"time":"2025-08-09T22:30:45.22214342+08:00","level":"INFO","msg":"handler: closed","stream_id":"81zz41tz"}
{"time":"2025-08-09T22:30:45.222171733+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"81zz41tz"}
{"time":"2025-08-09T22:30:45.222191906+08:00","level":"INFO","msg":"sender: closed","stream_id":"81zz41tz"}
{"time":"2025-08-09T22:30:45.22225556+08:00","level":"INFO","msg":"stream: closed","id":"81zz41tz"}
