{"time":"2025-08-12T17:58:31.620210061+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T17:58:31.751713816+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T17:58:31.751954537+08:00","level":"INFO","msg":"stream: created new stream","id":"mwa6v6t2"}
{"time":"2025-08-12T17:58:31.751989588+08:00","level":"INFO","msg":"stream: started","id":"mwa6v6t2"}
{"time":"2025-08-12T17:58:31.752071776+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"mwa6v6t2"}
{"time":"2025-08-12T17:58:31.75214074+08:00","level":"INFO","msg":"handler: started","stream_id":"mwa6v6t2"}
{"time":"2025-08-12T17:58:31.752214798+08:00","level":"INFO","msg":"sender: started","stream_id":"mwa6v6t2"}
{"time":"2025-08-12T17:58:31.753069367+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-12T17:58:56.819689893+08:00","level":"INFO","msg":"stream: closing","id":"mwa6v6t2"}
{"time":"2025-08-12T17:58:56.819952233+08:00","level":"INFO","msg":"handler: closed","stream_id":"mwa6v6t2"}
{"time":"2025-08-12T17:58:56.819978912+08:00","level":"INFO","msg":"sender: closed","stream_id":"mwa6v6t2"}
{"time":"2025-08-12T17:58:56.819985666+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"mwa6v6t2"}
{"time":"2025-08-12T17:58:56.820101104+08:00","level":"INFO","msg":"stream: closed","id":"mwa6v6t2"}
