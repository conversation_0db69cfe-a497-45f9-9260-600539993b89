{"time":"2025-08-12T02:20:08.842648579+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T02:20:08.975462974+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T02:20:08.975788559+08:00","level":"INFO","msg":"stream: created new stream","id":"f6ffpmj0"}
{"time":"2025-08-12T02:20:08.975833437+08:00","level":"INFO","msg":"stream: started","id":"f6ffpmj0"}
{"time":"2025-08-12T02:20:08.97594693+08:00","level":"INFO","msg":"sender: started","stream_id":"f6ffpmj0"}
{"time":"2025-08-12T02:20:08.975930304+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"f6ffpmj0"}
{"time":"2025-08-12T02:20:08.976117127+08:00","level":"INFO","msg":"handler: started","stream_id":"f6ffpmj0"}
{"time":"2025-08-12T02:20:08.977456601+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
