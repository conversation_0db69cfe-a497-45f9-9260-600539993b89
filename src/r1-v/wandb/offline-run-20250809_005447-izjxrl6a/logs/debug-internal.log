{"time":"2025-08-09T00:54:47.383424924+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-09T00:54:47.515685516+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-09T00:54:47.516047653+08:00","level":"INFO","msg":"stream: created new stream","id":"izjxrl6a"}
{"time":"2025-08-09T00:54:47.51609518+08:00","level":"INFO","msg":"stream: started","id":"izjxrl6a"}
{"time":"2025-08-09T00:54:47.516220567+08:00","level":"INFO","msg":"sender: started","stream_id":"izjxrl6a"}
{"time":"2025-08-09T00:54:47.516212324+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"izjxrl6a"}
{"time":"2025-08-09T00:54:47.516218344+08:00","level":"INFO","msg":"handler: started","stream_id":"izjxrl6a"}
{"time":"2025-08-09T00:54:47.517494178+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
