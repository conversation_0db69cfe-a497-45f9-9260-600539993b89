{"time":"2025-08-12T17:53:53.824358727+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T17:53:53.958209108+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T17:53:53.958511416+08:00","level":"INFO","msg":"stream: created new stream","id":"rph9hofy"}
{"time":"2025-08-12T17:53:53.958552511+08:00","level":"INFO","msg":"stream: started","id":"rph9hofy"}
{"time":"2025-08-12T17:53:53.958645896+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"rph9hofy"}
{"time":"2025-08-12T17:53:53.958708336+08:00","level":"INFO","msg":"handler: started","stream_id":"rph9hofy"}
{"time":"2025-08-12T17:53:53.95879387+08:00","level":"INFO","msg":"sender: started","stream_id":"rph9hofy"}
{"time":"2025-08-12T17:53:53.959699826+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-12T17:54:18.006752875+08:00","level":"INFO","msg":"stream: closing","id":"rph9hofy"}
{"time":"2025-08-12T17:54:18.006986038+08:00","level":"INFO","msg":"handler: closed","stream_id":"rph9hofy"}
{"time":"2025-08-12T17:54:18.007049464+08:00","level":"INFO","msg":"sender: closed","stream_id":"rph9hofy"}
{"time":"2025-08-12T17:54:18.007047523+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"rph9hofy"}
{"time":"2025-08-12T17:54:18.007195122+08:00","level":"INFO","msg":"stream: closed","id":"rph9hofy"}
