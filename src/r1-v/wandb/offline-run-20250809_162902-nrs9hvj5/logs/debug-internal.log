{"time":"2025-08-09T16:29:02.769016245+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-09T16:29:02.89889254+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-09T16:29:02.899245805+08:00","level":"INFO","msg":"stream: created new stream","id":"nrs9hvj5"}
{"time":"2025-08-09T16:29:02.899293292+08:00","level":"INFO","msg":"stream: started","id":"nrs9hvj5"}
{"time":"2025-08-09T16:29:02.899401292+08:00","level":"INFO","msg":"handler: started","stream_id":"nrs9hvj5"}
{"time":"2025-08-09T16:29:02.899393194+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"nrs9hvj5"}
{"time":"2025-08-09T16:29:02.89946129+08:00","level":"INFO","msg":"sender: started","stream_id":"nrs9hvj5"}
{"time":"2025-08-09T16:29:02.900512981+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
