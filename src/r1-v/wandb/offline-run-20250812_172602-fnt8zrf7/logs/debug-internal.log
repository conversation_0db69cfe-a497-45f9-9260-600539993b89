{"time":"2025-08-12T17:26:02.953587649+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T17:26:03.087905487+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T17:26:03.088156173+08:00","level":"INFO","msg":"stream: created new stream","id":"fnt8zrf7"}
{"time":"2025-08-12T17:26:03.088194105+08:00","level":"INFO","msg":"stream: started","id":"fnt8zrf7"}
{"time":"2025-08-12T17:26:03.088227319+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"fnt8zrf7"}
{"time":"2025-08-12T17:26:03.088308195+08:00","level":"INFO","msg":"sender: started","stream_id":"fnt8zrf7"}
{"time":"2025-08-12T17:26:03.088357555+08:00","level":"INFO","msg":"handler: started","stream_id":"fnt8zrf7"}
{"time":"2025-08-12T17:26:03.089268354+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
