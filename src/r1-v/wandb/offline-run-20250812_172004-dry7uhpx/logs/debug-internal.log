{"time":"2025-08-12T17:20:05.042735936+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T17:20:05.178362684+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T17:20:05.178616384+08:00","level":"INFO","msg":"stream: created new stream","id":"dry7uhpx"}
{"time":"2025-08-12T17:20:05.178656589+08:00","level":"INFO","msg":"stream: started","id":"dry7uhpx"}
{"time":"2025-08-12T17:20:05.178752626+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"dry7uhpx"}
{"time":"2025-08-12T17:20:05.178823244+08:00","level":"INFO","msg":"sender: started","stream_id":"dry7uhpx"}
{"time":"2025-08-12T17:20:05.178845835+08:00","level":"INFO","msg":"handler: started","stream_id":"dry7uhpx"}
{"time":"2025-08-12T17:20:05.180423421+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-12T17:20:19.203727234+08:00","level":"INFO","msg":"stream: closing","id":"dry7uhpx"}
{"time":"2025-08-12T17:20:19.204127588+08:00","level":"INFO","msg":"handler: closed","stream_id":"dry7uhpx"}
{"time":"2025-08-12T17:20:19.204171412+08:00","level":"INFO","msg":"sender: closed","stream_id":"dry7uhpx"}
{"time":"2025-08-12T17:20:19.204182176+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"dry7uhpx"}
{"time":"2025-08-12T17:20:19.204276633+08:00","level":"INFO","msg":"stream: closed","id":"dry7uhpx"}
