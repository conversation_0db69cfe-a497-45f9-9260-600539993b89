{"time":"2025-08-05T17:36:13.086938282+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-05T17:36:13.223737186+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-05T17:36:13.224081346+08:00","level":"INFO","msg":"stream: created new stream","id":"n8lljd8i"}
{"time":"2025-08-05T17:36:13.224125209+08:00","level":"INFO","msg":"stream: started","id":"n8lljd8i"}
{"time":"2025-08-05T17:36:13.224181248+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"n8lljd8i"}
{"time":"2025-08-05T17:36:13.224294697+08:00","level":"INFO","msg":"handler: started","stream_id":"n8lljd8i"}
{"time":"2025-08-05T17:36:13.224243297+08:00","level":"INFO","msg":"sender: started","stream_id":"n8lljd8i"}
{"time":"2025-08-05T17:36:13.225658122+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-05T23:48:49.206833666+08:00","level":"INFO","msg":"stream: closing","id":"n8lljd8i"}
{"time":"2025-08-05T23:48:49.207132254+08:00","level":"INFO","msg":"handler: closed","stream_id":"n8lljd8i"}
{"time":"2025-08-05T23:48:49.207172798+08:00","level":"INFO","msg":"sender: closed","stream_id":"n8lljd8i"}
{"time":"2025-08-05T23:48:49.207174454+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"n8lljd8i"}
{"time":"2025-08-05T23:48:49.207288539+08:00","level":"INFO","msg":"stream: closed","id":"n8lljd8i"}
