{"time":"2025-08-12T17:11:59.742131282+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T17:11:59.882135795+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T17:11:59.882465473+08:00","level":"INFO","msg":"stream: created new stream","id":"emx367qa"}
{"time":"2025-08-12T17:11:59.882511518+08:00","level":"INFO","msg":"stream: started","id":"emx367qa"}
{"time":"2025-08-12T17:11:59.882548513+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"emx367qa"}
{"time":"2025-08-12T17:11:59.882620321+08:00","level":"INFO","msg":"handler: started","stream_id":"emx367qa"}
{"time":"2025-08-12T17:11:59.882646298+08:00","level":"INFO","msg":"sender: started","stream_id":"emx367qa"}
{"time":"2025-08-12T17:11:59.883680845+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-12T17:12:57.978280106+08:00","level":"INFO","msg":"stream: closing","id":"emx367qa"}
{"time":"2025-08-12T17:12:57.978572445+08:00","level":"INFO","msg":"handler: closed","stream_id":"emx367qa"}
{"time":"2025-08-12T17:12:57.978606502+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"emx367qa"}
{"time":"2025-08-12T17:12:57.978614649+08:00","level":"INFO","msg":"sender: closed","stream_id":"emx367qa"}
{"time":"2025-08-12T17:12:57.978658638+08:00","level":"INFO","msg":"stream: closed","id":"emx367qa"}
