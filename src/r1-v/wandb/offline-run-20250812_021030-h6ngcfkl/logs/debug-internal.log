{"time":"2025-08-12T02:10:30.630230305+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T02:10:30.766579737+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T02:10:30.766928459+08:00","level":"INFO","msg":"stream: created new stream","id":"h6ngcfkl"}
{"time":"2025-08-12T02:10:30.766977412+08:00","level":"INFO","msg":"stream: started","id":"h6ngcfkl"}
{"time":"2025-08-12T02:10:30.767045261+08:00","level":"INFO","msg":"sender: started","stream_id":"h6ngcfkl"}
{"time":"2025-08-12T02:10:30.767040107+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"h6ngcfkl"}
{"time":"2025-08-12T02:10:30.767098059+08:00","level":"INFO","msg":"handler: started","stream_id":"h6ngcfkl"}
{"time":"2025-08-12T02:10:30.768242933+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-12T02:14:03.34192154+08:00","level":"INFO","msg":"stream: closing","id":"h6ngcfkl"}
{"time":"2025-08-12T02:14:03.342364821+08:00","level":"INFO","msg":"handler: closed","stream_id":"h6ngcfkl"}
{"time":"2025-08-12T02:14:03.342417939+08:00","level":"INFO","msg":"sender: closed","stream_id":"h6ngcfkl"}
{"time":"2025-08-12T02:14:03.342421513+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"h6ngcfkl"}
{"time":"2025-08-12T02:14:03.342556824+08:00","level":"INFO","msg":"stream: closed","id":"h6ngcfkl"}
