{"time":"2025-08-09T00:47:12.786595751+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-09T00:47:12.920460012+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-09T00:47:12.92077218+08:00","level":"INFO","msg":"stream: created new stream","id":"cj6kchd7"}
{"time":"2025-08-09T00:47:12.920818245+08:00","level":"INFO","msg":"stream: started","id":"cj6kchd7"}
{"time":"2025-08-09T00:47:12.92087786+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"cj6kchd7"}
{"time":"2025-08-09T00:47:12.920939343+08:00","level":"INFO","msg":"sender: started","stream_id":"cj6kchd7"}
{"time":"2025-08-09T00:47:12.921063033+08:00","level":"INFO","msg":"handler: started","stream_id":"cj6kchd7"}
{"time":"2025-08-09T00:47:12.922057742+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
