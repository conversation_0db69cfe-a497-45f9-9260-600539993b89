{"time":"2025-08-05T23:50:24.014869845+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-05T23:50:24.155893317+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-05T23:50:24.156250825+08:00","level":"INFO","msg":"stream: created new stream","id":"dlvj6asl"}
{"time":"2025-08-05T23:50:24.156300963+08:00","level":"INFO","msg":"stream: started","id":"dlvj6asl"}
{"time":"2025-08-05T23:50:24.15634023+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"dlvj6asl"}
{"time":"2025-08-05T23:50:24.15640049+08:00","level":"INFO","msg":"handler: started","stream_id":"dlvj6asl"}
{"time":"2025-08-05T23:50:24.156489438+08:00","level":"INFO","msg":"sender: started","stream_id":"dlvj6asl"}
{"time":"2025-08-05T23:50:24.157543225+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
