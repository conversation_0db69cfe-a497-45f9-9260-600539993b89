{"time":"2025-08-12T17:51:00.157045262+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T17:51:00.285781906+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T17:51:00.286014223+08:00","level":"INFO","msg":"stream: created new stream","id":"kuw7tga0"}
{"time":"2025-08-12T17:51:00.286050208+08:00","level":"INFO","msg":"stream: started","id":"kuw7tga0"}
{"time":"2025-08-12T17:51:00.286149086+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"kuw7tga0"}
{"time":"2025-08-12T17:51:00.286173857+08:00","level":"INFO","msg":"sender: started","stream_id":"kuw7tga0"}
{"time":"2025-08-12T17:51:00.286245026+08:00","level":"INFO","msg":"handler: started","stream_id":"kuw7tga0"}
{"time":"2025-08-12T17:51:00.287025586+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
