{"time":"2025-08-12T17:06:01.006275473+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T17:06:01.141667498+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T17:06:01.14196579+08:00","level":"INFO","msg":"stream: created new stream","id":"rqj32lyb"}
{"time":"2025-08-12T17:06:01.142015617+08:00","level":"INFO","msg":"stream: started","id":"rqj32lyb"}
{"time":"2025-08-12T17:06:01.142129238+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"rqj32lyb"}
{"time":"2025-08-12T17:06:01.142168331+08:00","level":"INFO","msg":"sender: started","stream_id":"rqj32lyb"}
{"time":"2025-08-12T17:06:01.142271057+08:00","level":"INFO","msg":"handler: started","stream_id":"rqj32lyb"}
{"time":"2025-08-12T17:06:01.143019891+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-12T17:06:25.186918014+08:00","level":"INFO","msg":"stream: closing","id":"rqj32lyb"}
{"time":"2025-08-12T17:06:25.187244352+08:00","level":"INFO","msg":"handler: closed","stream_id":"rqj32lyb"}
{"time":"2025-08-12T17:06:25.187279469+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"rqj32lyb"}
{"time":"2025-08-12T17:06:25.187320457+08:00","level":"INFO","msg":"sender: closed","stream_id":"rqj32lyb"}
{"time":"2025-08-12T17:06:25.187366529+08:00","level":"INFO","msg":"stream: closed","id":"rqj32lyb"}
