{"time":"2025-08-12T18:17:21.913521167+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-12T18:17:22.048755241+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-12T18:17:22.049133164+08:00","level":"INFO","msg":"stream: created new stream","id":"odk2g6hj"}
{"time":"2025-08-12T18:17:22.049186413+08:00","level":"INFO","msg":"stream: started","id":"odk2g6hj"}
{"time":"2025-08-12T18:17:22.049281378+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"odk2g6hj"}
{"time":"2025-08-12T18:17:22.049291293+08:00","level":"INFO","msg":"handler: started","stream_id":"odk2g6hj"}
{"time":"2025-08-12T18:17:22.049371234+08:00","level":"INFO","msg":"sender: started","stream_id":"odk2g6hj"}
{"time":"2025-08-12T18:17:22.050371051+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-12T18:17:50.115413663+08:00","level":"INFO","msg":"stream: closing","id":"odk2g6hj"}
{"time":"2025-08-12T18:17:50.115753521+08:00","level":"INFO","msg":"handler: closed","stream_id":"odk2g6hj"}
{"time":"2025-08-12T18:17:50.115781153+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"odk2g6hj"}
{"time":"2025-08-12T18:17:50.115789772+08:00","level":"INFO","msg":"sender: closed","stream_id":"odk2g6hj"}
{"time":"2025-08-12T18:17:50.115873647+08:00","level":"INFO","msg":"stream: closed","id":"odk2g6hj"}
