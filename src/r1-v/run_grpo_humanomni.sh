cd src/r1-v
export PYTHONPATH=/data/wuyang/R1-Omni-main:/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1:$PYTHONPATH
export DEBUG_MODE="true" # Enable Debug if you want to see the rollout of model during RL
export LOG_PATH="./logs/humanomni_emotion_emer_1format_withpath_withchoice_w5w5_easyhardtag_test3.txt"
export HF_HOME=/mnt/data/jiaxing.zjx/cache/huggingface/
mkdir -p ./logs

# 打印日志路径以确认
echo "Log path set to: $LOG_PATH"

WANDB_MODE=offline torchrun --nproc_per_node="4" \
    --nnodes="1" \
    --node_rank="0" \
    --master_addr="127.0.0.1" \
    --master_port="12346" \
    src/open_r1/grpo.py \
    --output_dir /data/wuyang/R1-Omni-main/Outputs/eval_test_w5w5_test3 \
    --model_name_or_path /data/wuyang/PLM/EMER-SFT-0.5B \
    --dataset_name /data/wuyang/merged_dataset_with_difficulty_mapped.json \
    --deepspeed local_scripts/zero3.json \
    --max_prompt_length 512 \
    --max_completion_length 512 \
    --per_device_train_batch_size 1 \
    --gradient_accumulation_steps 2 \
    --logging_steps 1 \
    --bf16 \
    --report_to wandb \
    --gradient_checkpointing false \
    --attn_implementation flash_attention_2 \
    --max_pixels 401408 \
    --num_train_epochs 2 \
    --run_name Qwen2-VL-2B-GRPO-emotion \
    --save_steps 500 \
    --save_only_model true \
    --num_generations 8 \
    # --reward_funcs accuracy format length \  # Enable accuracy, format, and length rewards
    # --accuracy_threshold 0.6  # Threshold for length reward activation (default: 0.5)
     # number of outputs G in grpo, reduce it would lead to faster training and smaller memory cost but higher variance